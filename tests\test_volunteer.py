import unittest
import json
import os
import tempfile
from unittest.mock import Mock, patch, MagicMock
from datetime import datetime, timedelta
import sys

# Add parent directory to path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))

class TestVolunteerSystem(unittest.TestCase):
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create temporary directory for test data
        self.test_dir = tempfile.mkdtemp()
        self.volunteer_data_path = os.path.join(self.test_dir, "volunteer_data.json")
        
        # Mock Google Calendar service
        self.mock_service = Mock()
        self.mock_events = Mock()
        self.mock_service.events.return_value = self.mock_events
        
        # Test data
        self.test_volunteer_email = "<EMAIL>"
        self.test_date = "2025-01-20"
        self.test_time = "10:00"
        self.test_calendar_id = "test_calendar_id"
        
        # Sample volunteer data structure
        self.sample_volunteer_data = {
            "volunteers": [],
            "slot_assignments": {}
        }
    
    def tearDown(self):
        """Clean up after each test."""
        # Remove temporary test files
        if os.path.exists(self.volunteer_data_path):
            os.remove(self.volunteer_data_path)
        os.rmdir(self.test_dir)
    
    def create_test_volunteer_data(self, data=None):
        """Helper method to create test volunteer data file."""
        if data is None:
            data = self.sample_volunteer_data
        with open(self.volunteer_data_path, 'w') as f:
            json.dump(data, f, indent=2)
    
    # ========== RED PHASE: Write failing tests first ==========
    
    def test_check_slot_availability_free_slot(self):
        """Test that a free slot returns True."""
        # This test will fail initially - we haven't implemented the function yet
        from volunteer import check_slot_availability
        
        # Mock empty calendar response
        self.mock_events.list.return_value.execute.return_value = {"items": []}
        
        # Create empty volunteer data
        self.create_test_volunteer_data()
        
        with patch('volunteer.VOLUNTEER_DATA_PATH', self.volunteer_data_path):
            available, message = check_slot_availability(
                self.mock_service, 
                self.test_calendar_id, 
                self.test_date, 
                self.test_time
            )
        
        self.assertTrue(available)
        self.assertEqual(message, "Slot available")
    
    def test_check_slot_availability_occupied_slot(self):
        """Test that an occupied slot returns False."""
        from volunteer import check_slot_availability
        
        # Mock calendar response with existing event
        self.mock_events.list.return_value.execute.return_value = {
            "items": [{"summary": "Existing event"}]
        }
        
        self.create_test_volunteer_data()
        
        with patch('volunteer.VOLUNTEER_DATA_PATH', self.volunteer_data_path):
            available, message = check_slot_availability(
                self.mock_service, 
                self.test_calendar_id, 
                self.test_date, 
                self.test_time
            )
        
        self.assertFalse(available)
        self.assertEqual(message, "Slot already taken")
    
    def test_check_volunteer_conflicts_no_conflicts(self):
        """Test volunteer with no existing conflicts."""
        from volunteer import check_volunteer_conflicts
        
        self.create_test_volunteer_data()
        
        with patch('volunteer.VOLUNTEER_DATA_PATH', self.volunteer_data_path):
            conflict, message = check_volunteer_conflicts(
                self.test_volunteer_email, 
                self.test_date, 
                self.test_time
            )
        
        self.assertFalse(conflict)
        self.assertEqual(message, "No conflicts found")
    
    def test_check_volunteer_conflicts_same_slot(self):
        """Test volunteer trying to book the same slot twice."""
        from volunteer import check_volunteer_conflicts
        
        # Create data with existing volunteer slot
        data_with_volunteer = {
            "volunteers": [{
                "email": self.test_volunteer_email,
                "slots": [{
                    "date": self.test_date,
                    "time": self.test_time,
                    "calendar_event_id": "test_event_123",
                    "status": "active"
                }]
            }],
            "slot_assignments": {}
        }
        
        self.create_test_volunteer_data(data_with_volunteer)
        
        with patch('volunteer.VOLUNTEER_DATA_PATH', self.volunteer_data_path):
            conflict, message = check_volunteer_conflicts(
                self.test_volunteer_email, 
                self.test_date, 
                self.test_time
            )
        
        self.assertTrue(conflict)
        self.assertEqual(message, "You already volunteered for this slot")
    
    def test_volunteer_for_slot_success(self):
        """Test successful volunteer registration."""
        from volunteer import volunteer_for_slot
        
        # Mock successful calendar event creation
        mock_event = {"id": "new_event_123"}
        self.mock_events.insert.return_value.execute.return_value = mock_event
        self.mock_events.list.return_value.execute.return_value = {"items": []}
        
        self.create_test_volunteer_data()
        
        with patch('volunteer.VOLUNTEER_DATA_PATH', self.volunteer_data_path):
            success, message = volunteer_for_slot(
                self.mock_service,
                self.test_calendar_id,
                self.test_date,
                self.test_time,
                self.test_volunteer_email
            )
        
        self.assertTrue(success)
        self.assertEqual(message, "Successfully volunteered for slot")
        
        # Verify data was saved
        with open(self.volunteer_data_path, 'r') as f:
            saved_data = json.load(f)
        
        self.assertEqual(len(saved_data["volunteers"]), 1)
        self.assertEqual(saved_data["volunteers"][0]["email"], self.test_volunteer_email)
    
    def test_volunteer_for_slot_slot_taken(self):
        """Test volunteer registration for already taken slot."""
        from volunteer import volunteer_for_slot
        
        # Mock calendar response with existing event
        self.mock_events.list.return_value.execute.return_value = {
            "items": [{"summary": "Existing event"}]
        }
        
        self.create_test_volunteer_data()
        
        with patch('volunteer.VOLUNTEER_DATA_PATH', self.volunteer_data_path):
            success, message = volunteer_for_slot(
                self.mock_service,
                self.test_calendar_id,
                self.test_date,
                self.test_time,
                self.test_volunteer_email
            )
        
        self.assertFalse(success)
        self.assertEqual(message, "Slot already taken")
    
    def test_save_volunteer_data_new_volunteer(self):
        """Test saving data for a new volunteer."""
        from volunteer import save_volunteer_data
        
        self.create_test_volunteer_data()
        
        volunteer_info = {
            "email": self.test_volunteer_email,
            "date": self.test_date,
            "time": self.test_time,
            "event_id": "test_event_123",
            "status": "active"
        }
        
        with patch('volunteer.VOLUNTEER_DATA_PATH', self.volunteer_data_path):
            save_volunteer_data(volunteer_info)
        
        # Verify data was saved correctly
        with open(self.volunteer_data_path, 'r') as f:
            saved_data = json.load(f)
        
        self.assertEqual(len(saved_data["volunteers"]), 1)
        self.assertEqual(saved_data["volunteers"][0]["email"], self.test_volunteer_email)
        
        slot_key = f"{self.test_date}_{self.test_time}"
        self.assertIn(slot_key, saved_data["slot_assignments"])
    
    def test_load_volunteer_data_file_not_exists(self):
        """Test loading volunteer data when file doesn't exist."""
        from volunteer import load_volunteer_data
        
        # Don't create the file
        with patch('volunteer.VOLUNTEER_DATA_PATH', self.volunteer_data_path):
            data = load_volunteer_data()
        
        expected_structure = {
            "volunteers": [],
            "slot_assignments": {}
        }
        self.assertEqual(data, expected_structure)


if __name__ == '__main__':
    unittest.main()
