import unittest
import os
import tempfile
import sys
import json
from unittest.mock import patch

# Add parent directory to path to import our modules
sys.path.append(os.path.dirname(os.path.dirname(os.path.abspath(__file__))))


class TestVolunteerDataStorage(unittest.TestCase):
    """Test volunteer data storage functionality."""
    
    def setUp(self):
        """Set up test fixtures before each test method."""
        # Create temporary directory for test data
        self.test_dir = tempfile.mkdtemp()
        self.volunteer_data_path = os.path.join(self.test_dir, "volunteer_data.json")
    
    def tearDown(self):
        """Clean up after each test."""
        # Remove temporary test files
        if os.path.exists(self.volunteer_data_path):
            os.remove(self.volunteer_data_path)
        os.rmdir(self.test_dir)
    
    def test_load_volunteer_data_when_file_does_not_exist(self):
        """
        Test that loading volunteer data returns empty structure when file doesn't exist.
        
        This is our first test - it should fail because we haven't created the 
        load_volunteer_data function yet.
        """
        # Import the function we want to test (this will fail initially)
        from volunteer import load_volunteer_data
        
        # Call the function with a non-existent file path
        # We'll use patch to override the file path for testing
        with patch('volunteer.VOLUNTEER_DATA_PATH', self.volunteer_data_path):
            result = load_volunteer_data()
        
        # Expected structure when no file exists
        expected = {
            "volunteers": [],
            "slot_assignments": {}
        }
        
        # Assert that we get the expected empty structure
        self.assertEqual(result, expected)
        self.assertIsInstance(result["volunteers"], list)
        self.assertIsInstance(result["slot_assignments"], dict)
        self.assertEqual(len(result["volunteers"]), 0)
        self.assertEqual(len(result["slot_assignments"]), 0)

    def test_load_volunteer_data_when_file_exists(self):
        """
        Test that loading volunteer data returns the correct data when file exists.
        
        This test should also fail initially because we haven't implemented the 
        functionality to save and load data yet.
        """
        test_data = {
        "volunteers": [
            {
                "email": "<EMAIL>", 
                "name": "Test User",
                "slots": []
            }
        ],
        "slot_assignments": {}
    }
        
        #import data to file
        with open(self.volunteer_data_path, 'w') as f:
            json.dump(test_data, f)

        with open(self.volunteer_data_path, 'r') as f:
            data=json.load(f)
            
        self.assertEqual(data, test_data)

    def test_save_volunteer_data_creates_new_file(self):
        """Test that saving volunteer data creates a new file when none exists."""
        
        # Step 1: Make sure no file exists initially
        self.assertFalse(os.path.exists(self.volunteer_data_path))
        
        # Step 2: Create volunteer info to save
        volunteer_info = {
        "volunteers": [
            {
                "email": "<EMAIL>", 
                "name": "Test User",
                "slots": []
            }
        ],
        "slot_assignments": {}
    }
        
        # Step 3: Try to import and call save function (this will fail initially)
        from volunteer import save_volunteer_data
        
        # Step 4: Call the save function
        with patch('volunteer.VOLUNTEER_DATA_PATH', self.volunteer_data_path):
            save_volunteer_data(volunteer_info)
        
        # Step 5: Verify file was created
        self.assertTrue(os.path.exists(self.volunteer_data_path))
        
        # Step 6: Verify the data was saved correctly
        with open(self.volunteer_data_path, 'r') as f:
            saved_data = json.load(f)
        
        # Check the structure
        self.assertIn("volunteers", saved_data)
        self.assertIn("slot_assignments", saved_data)
        self.assertEqual(len(saved_data["volunteers"]), 1)
        self.assertEqual(saved_data["volunteers"][0]["email"], ".com")

    



if __name__ == '__main__':
    unittest.main()

