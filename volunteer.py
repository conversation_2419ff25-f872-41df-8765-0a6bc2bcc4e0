"""
Volunteer management system for Code Clinic booking.
"""

import os
import json

# Configuration - following the same pattern as clinic.py
VOLUNTEER_DATA_PATH = os.path.expanduser("~/.clinicbooker/volunteer_data.json")


def load_volunteer_data():
    """
    Load volunteer data from local JSON file.
    Returns empty structure if file doesn't exist.
    """
    # If file doesn't exist, return empty structure
    if not os.path.exists(VOLUNTEER_DATA_PATH):
        return {
            "volunteers": [],
            "slot_assignments": {}
        }
    
    # If file exists, load and return the data
    try:
        with open(VOLUNTEER_DATA_PATH, 'r') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        # If file is corrupted or can't be read, return empty structure
        return {
            "volunteers": [],
            "slot_assignments": {}
        }
