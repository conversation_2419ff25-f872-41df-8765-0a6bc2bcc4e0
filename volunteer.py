"""
Volunteer management system for Code Clinic booking.
Handles volunteer registration, slot availability checking, and calendar integration.
"""

import os
import json
import datetime
from typing import <PERSON><PERSON>, Dict, Any
from googleapiclient.errors import HttpError

# Configuration
VOLUNTEER_DATA_PATH = os.path.expanduser("~/.clinicbooker/volunteer_data.json")


class VolunteerBookingError(Exception):
    """Custom exception for volunteer booking errors."""
    pass


def load_volunteer_data() -> Dict[str, Any]:
    """
    Load volunteer data from local JSON file.
    Returns default structure if file doesn't exist.
    """
    if not os.path.exists(VOLUNTEER_DATA_PATH):
        return {
            "volunteers": [],
            "slot_assignments": {}
        }
    
    try:
        with open(VOLUNTEER_DATA_PATH, 'r') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError):
        return {
            "volunteers": [],
            "slot_assignments": {}
        }


def save_volunteer_data(volunteer_info: Dict[str, str]) -> None:
    """
    Save volunteer information to local data file.
    
    Args:
        volunteer_info: Dict containing email, date, time, event_id, status
    """
    # Ensure directory exists
    os.makedirs(os.path.dirname(VOLUNTEER_DATA_PATH), exist_ok=True)
    
    # Load existing data
    data = load_volunteer_data()
    
    # Add to volunteers list
    volunteer_found = False
    for volunteer in data["volunteers"]:
        if volunteer["email"] == volunteer_info["email"]:
            volunteer["slots"].append({
                "date": volunteer_info["date"],
                "time": volunteer_info["time"],
                "calendar_event_id": volunteer_info["event_id"],
                "status": volunteer_info["status"]
            })
            volunteer_found = True
            break
    
    if not volunteer_found:
        data["volunteers"].append({
            "email": volunteer_info["email"],
            "slots": [{
                "date": volunteer_info["date"],
                "time": volunteer_info["time"],
                "calendar_event_id": volunteer_info["event_id"],
                "status": volunteer_info["status"]
            }]
        })
    
    # Add to slot assignments
    slot_key = f"{volunteer_info['date']}_{volunteer_info['time']}"
    data["slot_assignments"][slot_key] = {
        "volunteer_email": volunteer_info["email"],
        "event_id": volunteer_info["event_id"],
        "created_at": datetime.datetime.now().isoformat()
    }
    
    # Save to file
    with open(VOLUNTEER_DATA_PATH, 'w') as f:
        json.dump(data, f, indent=2)


def check_slot_availability(service, calendar_id: str, date: str, time: str) -> Tuple[bool, str]:
    """
    Check if a time slot is available for volunteering.
    
    Args:
        service: Google Calendar API service object
        calendar_id: Calendar ID to check
        date: Date in YYYY-MM-DD format
        time: Time in HH:MM format
    
    Returns:
        Tuple of (is_available: bool, message: str)
    """
    try:
        # Convert date/time to datetime objects
        start_datetime = datetime.datetime.fromisoformat(f"{date}T{time}:00")
        end_datetime = start_datetime + datetime.timedelta(hours=1)
        
        # Query Google Calendar for existing events
        events_result = service.events().list(
            calendarId=calendar_id,
            timeMin=start_datetime.isoformat(),
            timeMax=end_datetime.isoformat(),
            singleEvents=True,
            orderBy='startTime'
        ).execute()
        
        events = events_result.get('items', [])
        
        # Check if there are any events in this time slot
        if events:
            return False, "Slot already taken"
        
        # Check local volunteer data for conflicts
        volunteer_data = load_volunteer_data()
        slot_key = f"{date}_{time}"
        
        if slot_key in volunteer_data["slot_assignments"]:
            return False, "Slot already taken"
        
        return True, "Slot available"
        
    except Exception as e:
        return False, f"Error checking availability: {str(e)}"


def check_volunteer_conflicts(volunteer_email: str, date: str, time: str) -> Tuple[bool, str]:
    """
    Check if volunteer has conflicts with the requested slot.
    
    Args:
        volunteer_email: Email of the volunteer
        date: Date in YYYY-MM-DD format
        time: Time in HH:MM format
    
    Returns:
        Tuple of (has_conflict: bool, message: str)
    """
    volunteer_data = load_volunteer_data()
    
    for volunteer in volunteer_data["volunteers"]:
        if volunteer["email"] == volunteer_email:
            for slot in volunteer["slots"]:
                if slot["date"] == date and slot["time"] == time:
                    return True, "You already volunteered for this slot"
                
                # Check for time conflicts (overlapping hours)
                # For now, we'll just check exact time matches
                # TODO: Implement proper time overlap checking
                
    return False, "No conflicts found"


def add_volunteer_to_calendar(service, calendar_id: str, event_details: Dict[str, Any]) -> Dict[str, Any]:
    """
    Add volunteer event to Google Calendar.
    
    Args:
        service: Google Calendar API service object
        calendar_id: Calendar ID to add event to
        event_details: Event details dictionary
    
    Returns:
        Created event object
    
    Raises:
        VolunteerBookingError: If calendar operation fails
    """
    try:
        event = service.events().insert(
            calendarId=calendar_id,
            body=event_details
        ).execute()
        
        return event
        
    except HttpError as e:
        raise VolunteerBookingError(f"Failed to add to calendar: {e}")


def volunteer_for_slot(service, calendar_id: str, date: str, time: str, volunteer_email: str) -> Tuple[bool, str]:
    """
    Main function to register a volunteer for a specific time slot.
    
    Args:
        service: Google Calendar API service object
        calendar_id: Calendar ID for the clinic calendar
        date: Date in YYYY-MM-DD format
        time: Time in HH:MM format
        volunteer_email: Email of the volunteer
    
    Returns:
        Tuple of (success: bool, message: str)
    """
    try:
        # Step 1: Validate inputs (basic validation)
        if not date or not time or not volunteer_email:
            return False, "Invalid date, time, or email"
        
        # Step 2: Check slot availability
        available, availability_message = check_slot_availability(service, calendar_id, date, time)
        if not available:
            return False, availability_message
        
        # Step 3: Check volunteer conflicts
        conflict, conflict_message = check_volunteer_conflicts(volunteer_email, date, time)
        if conflict:
            return False, conflict_message
        
        # Step 4: Create calendar event
        start_datetime = f"{date}T{time}:00"
        # Add one hour for end time
        start_dt = datetime.datetime.fromisoformat(start_datetime)
        end_dt = start_dt + datetime.timedelta(hours=1)
        end_datetime = end_dt.isoformat()
        
        event_details = {
            "summary": f"Code Clinic - Volunteer ({volunteer_email})",
            "start": {"dateTime": start_datetime},
            "end": {"dateTime": end_datetime},
            "description": f"Volunteering for Code Clinic session"
        }
        
        calendar_event = add_volunteer_to_calendar(service, calendar_id, event_details)
        
        # Step 5: Save to local data
        volunteer_info = {
            "email": volunteer_email,
            "date": date,
            "time": time,
            "event_id": calendar_event["id"],
            "status": "active"
        }
        
        save_volunteer_data(volunteer_info)
        
        return True, "Successfully volunteered for slot"
        
    except Exception as e:
        return False, f"Error volunteering for slot: {str(e)}"
