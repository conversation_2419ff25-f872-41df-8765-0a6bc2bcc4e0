"""
Volunteer management system for Code Clinic booking.
"""
import os
import json
import sys

# Configuration - following the same pattern as clinic.py
VOLUNTEER_DATA_PATH = os.path.expanduser("~/.clinicbooker/volunteer_data.json")

#load data
def load_volunteer_data():
    """
    Load volunteer data from local JSON file.
    Returns empty structure if file doesn't exist.
    """
    # If file doesn't exist, return empty structure
    if not os.path.exists(VOLUNTEER_DATA_PATH):
        return {
            "volunteers": [],
            "slot_assignments": {}
        }
    
    # If file exists, load and return the data
    try:
        with open(VOLUNTEER_DATA_PATH, 'r') as f:
            return json.load(f)
    except (json.JSONDecodeError, FileNotFoundError) as e:
        # If file is corrupted or can't be read, return empty structure
        print(f"Error loading volunteer data: {e}")
        return {
            "volunteers": [],
            "slot_assignments": {}
        }
    
#save data
def save_volunteer_data(data):
    try:
        with open(VOLUNTEER_DATA_PATH, 'w') as f:
            json.dump(data, f, indent=2)
    except Exception as e:
        print(f"Error saving volunteer data: {e}")

        



