import unittest
import os
import local_storage

class TestLocalStorage(unittest.TestCase):
    def setUp(self):
        self.sample_events = [
            {"start": {"dateTime": "2025-05-27T10:00:00Z"}, "summary": "Event One"},
            {"start": {"date": "2025-05-28"}, "summary": "Event Two"}
        ]
        self.alt_events = [
            {"start": {"dateTime": "2025-05-29T10:00:00Z"}, "summary": "Different Event"}
        ]
        self.path = local_storage.DATA_FILE_PATH
        # Ensure clean test environment
        if os.path.exists(self.path):
            os.remove(self.path)

    def tearDown(self):
        # Clean up after each test
        if os.path.exists(self.path):
            os.remove(self.path)

    def test_save_and_load_events(self):
        local_storage.save_events(self.sample_events)
        loaded_events = local_storage.load_events()
        self.assertIsNotNone(loaded_events, "Loaded events should not be None")
        self.assertEqual(self.sample_events, loaded_events, "Saved and loaded events should be the same")

    def test_load_events_when_file_missing(self):
        if os.path.exists(self.path):
            os.remove(self.path)
            result = local_storage.load_events()
            self.assertEqual(result, [], "Loading from a missing file should return an empty list")


    def test_events_are_different_when_file_missing(self):
        if os.path.exists(self.path):
            os.remove(self.path)
        self.assertTrue(local_storage.events_are_different(self.sample_events),
                        "Should detect difference when no cache exists")

    def test_events_are_same_after_saving(self):
        local_storage.save_events(self.sample_events)
        self.assertFalse(local_storage.events_are_different(self.sample_events),
                         "Should detect no difference after saving same data")

    def test_events_are_different_after_change(self):
        local_storage.save_events(self.sample_events)
        self.assertTrue(local_storage.events_are_different(self.alt_events),
                        "Should detect difference when new data differs from saved")

if __name__ == "__main__":
    unittest.main()
