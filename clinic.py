import os
import json
import datetime
import argparse
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build
from config import configure

# ---------- CONFIGURATION ----------
SCOPES = ['https://www.googleapis.com/auth/calendar']
TOKEN_PATH = os.path.expanduser("~/.secrets/token.json")  # Path to store OAuth tokens
CLIENT_SECRET_PATH = os.path.expanduser("~/.secrets/client_credentials.json")

# Cache file path
CACHE_PATH = os.path.expanduser("~/.clinicbooker/calendar_data.json")

# ---------- AUTHENTICATION ----------
def load_credentials():
    if os.path.exists(TOKEN_PATH):
        with open(TOKEN_PATH, "r") as token_file:
            creds_data = json.load(token_file)
            return Credentials.from_authorized_user_info(creds_data, SCOPES)
    return None

def save_credentials(creds):
    with open(TOKEN_PATH, "w") as token_file:
        token_file.write(creds.to_json())

def delete_credentials():
    if os.path.exists(TOKEN_PATH):
        os.remove(TOKEN_PATH)

def login():
    flow = InstalledAppFlow.from_client_secrets_file(CLIENT_SECRET_PATH, SCOPES)
    creds = flow.run_local_server(port=0)
    save_credentials(creds)
    return creds

# ---------- Calendar Functions ----------
def get_upcoming_events(service, calendar_id='primary', days=7):
    """Fetch events for next 'days' days from Google Calendar."""
    daily_events = {}
    now = datetime.datetime.now(datetime.timezone.utc)
    for i in range(days):
        day = now + datetime.timedelta(days=i)
        start_day = day.replace(hour=0, minute=0, second=0, microsecond=0)
        end_day = start_day + datetime.timedelta(days=1)
        time_min = start_day.isoformat()
        time_max = end_day.isoformat()
        try:
            events_result = (
                service.events()
                .list(
                    calendarId=calendar_id,
                    timeMin=time_min,
                    timeMax=time_max,
                    singleEvents=True,
                    orderBy="startTime",
                )
                .execute()
            )
            events = events_result.get("items", [])
            # Use ISO string as key instead of date object
            daily_events[day.date().isoformat()] = events
        except Exception as e:
            print("Error fetching events:", e)
    return daily_events

def display_events(daily_events):
    """Display events in sorted order."""
    if not daily_events:
        print("No upcoming events found.")
        return
    # Sort keys as dates
    for day_str in sorted(daily_events.keys()):
        try:
            day = datetime.datetime.fromisoformat(day_str).date()
        except Exception:
            day = day_str  # fallback if parsing fails
        events = daily_events[day_str]
        print(f"\n📅 {day.strftime('%A, %d/%m/%Y') if hasattr(day, 'strftime') else day}:")
        if not events:
            print("No upcoming events.")
            continue
        from prettytable import PrettyTable
        table = PrettyTable(["Time", "Scheduled"])
        for event in events:
            start_raw = event["start"].get("dateTime") or event["start"].get("date")
            try:
                dt = datetime.datetime.fromisoformat(start_raw)
                if "T" in start_raw:
                    start_str = dt.strftime("%I:%M %p")
                else:
                    start_str = dt.strftime("%A (All Day)")
            except:
                start_str = start_raw
            summary = event.get("summary", "No Title")
            table.add_row([start_str, summary])
        print(table)

# ---------- Local Cache Functions ----------
def save_events(events):
    os.makedirs(os.path.dirname(CACHE_PATH), exist_ok=True)
    with open(CACHE_PATH, "w") as f:
        json.dump(events, f, indent=2)

def load_events():
    if not os.path.exists(CACHE_PATH):
        return []
    try:
        with open(CACHE_PATH, "r") as f:
            return json.load(f)
    except json.JSONDecodeError:
        return []

def events_are_different(new_events):
    old_events = load_events()
    return new_events != old_events

# ---------- Fetch, Cache, Display ----------
def fetch_update_display_calendar(service):
    print("Fetching latest events from Google Calendar...")
    new_events = get_upcoming_events(service, days=7)
    if events_are_different(new_events):
        print("New data detected. Updating cache...")
        save_events(new_events)
        events_to_show = new_events
    else:
        print("No new data. Using cached events.")
        events_to_show = load_events()
    display_events(events_to_show)

# ---------- Main Loop ----------
def main():
    parser = argparse.ArgumentParser(description="Coding Clinic Booking CLI")
    subparsers = parser.add_subparsers(dest="command")

    # Config command
    config_parser = subparsers.add_parser("config", help="Set up Google Calendar connection")
    config_parser.add_argument("--setup", action="store_true", help="Run configuration setup")

    # Login command
    login_parser = subparsers.add_parser("login", help="Authenticate with Google and save credentials")

    # View command
    view_parser = subparsers.add_parser("view", help="View available slots (default: next 7 days)")
    view_parser.add_argument("--days", type=int, default=7, help="Number of days to view")
    view_parser.add_argument("--calendar", choices=["user", "clinic"], help="Which calendar to view: user or clinic")

    args = parser.parse_args()

    # Add UI banner for all commands
    print("\033[92m" + "="*60 + "\033[0m")
    print("\033[92m   ____ ____  ____  ____     ____ _ _      _      _      \033[0m")
    print("\033[92m  / ___|  _ \\|  _ \\|  _ \\   / ___(_) | ___| | ___| |__   \033[0m")
    print("\033[92m | |   | | | | | | | | | | | |   | | |/ _ \\ |/ _ \\ '_ \\  \033[0m")
    print("\033[92m | |___| |_| | |_| | |_| | | |___| | |  __/ |  __/ |_) | \033[0m")
    print("\033[92m  \\____|____/|____/|____/   \\____|_|_|\\___|_|\\___|_.__/  \033[0m")
    print("\033[92m                CODE CLINICS BOOKING CLI\033[0m")
    print("\033[92m" + "="*60 + "\033[0m")
    print("\033[1;97m🎉 Welcome to Code Clinics! Your friendly help desk for all things coding. 🎉\033[0m")
    print("\n\033[1;97mCommands:\033[0m")
    print("  \033[94mconfig --setup\033[0m     Set up Google Calendar connection")
    print("  \033[94mlogin\033[0m              Authenticate with Google and save credentials")
    print("  \033[94mview [--days N] [--calendar user|clinic]\033[0m    View available slots (default: next 7 days)")
    print("  \033[94m-h, --help\033[0m         Show this help message and exit\n")

    if args.command == "config" and args.setup:
        configure()
        print("Configuration complete. Config file created at ~/.clinic-config.")
    elif args.command == "login":
        creds = login()
        if creds and creds.valid:
            print("Login successful. Credentials saved to token.json.")
        else:
            print("Login failed.")
    elif args.command == "view":
        from config import CONFIG_PATH
        if not os.path.exists(CONFIG_PATH):
            print("❗ Config file not found. Please run 'clinic.py config --setup' first.")
            return
        with open(CONFIG_PATH) as f:
            config = json.load(f)
        user_calendar_id = config.get("user_calendar_id")
        clinic_calendar_id = config.get("clinic_calendar_id")
        creds = load_credentials()
        if not creds or not creds.valid:
            print("❗ You must be logged in to view the calendar.")
            return
        service = build('calendar', 'v3', credentials=creds)
        # Determine which calendar to show
        calendar_choice = args.calendar
        if not calendar_choice:
            print("Which calendar would you like to view?")
            print("  1. User Calendar")
            print("  2. Clinic Calendar")
            choice = input("Enter 1 or 2: ").strip()
            if choice == "1":
                calendar_choice = "user"
            elif choice == "2":
                calendar_choice = "clinic"
            else:
                print("Invalid choice. Exiting.")
                return
        if calendar_choice == "user":
            print("\n📅 Coding Clinic Availability (User Calendar):")
            user_events = get_upcoming_events(service, calendar_id=user_calendar_id, days=args.days)
            display_events(user_events)
        else:
            print("\n📅 Coding Clinic Availability (Clinic Calendar):")
            clinic_events = get_upcoming_events(service, calendar_id=clinic_calendar_id, days=args.days)
            display_events(clinic_events)
    else:
        parser.print_help()

if __name__ == "__main__":
    main()