import os
import unittest
from config import configure
from auth import login
import json

class TestConfig(unittest.TestCase):

    def setUp(self):
        # Set up a test user and ensure a clean environment
        self.test_user = 'testuser'
        self.credentials_dir = os.path.expanduser("~/.clinic_credentials")
        # Make sure the credentials directory exists
        if not os.path.exists(self.credentials_dir):
            os.makedirs(self.credentials_dir)

        # Cleanup any existing test credentials before each test
        creds_path = os.path.join(self.credentials_dir, f"{self.test_user}.json")
        if os.path.exists(creds_path):
            os.remove(creds_path)
            
    def test_login(self):
        # Test that login creates a credentials file
        result = login()
        self.assertEqual(result, 'Login successful')
        creds_path = os.path.join(self.credentials_dir, f"{self.test_user}.json")
        self.assertTrue(os.path.exists(creds_path), "Credentials file was not created on login.")

    #verify and logout
    def test_verification(self):
        pass

    def test_config_file_created(self):
        """Test that configure() creates the main config file in the user's home directory."""
        config_path = os.path.expanduser("~/.clinic-config")
        # Remove config file if it exists
        if os.path.exists(config_path):
            os.remove(config_path)

        try:
            configure()
        except Exception:
            pass  # check file creation
        self.assertTrue(os.path.exists(config_path), "Config file was not created by configure().")

    def test_config_file_contains_required_keys(self):
        """Test that the config file contains required calendar ID keys after configure()."""
        config_path = os.path.expanduser("~/.clinic-config")
        # Remove config file if it exists
        if os.path.exists(config_path):
            os.remove(config_path)
        from config import configure
        configure()
        self.assertTrue(os.path.exists(config_path), "Config file was not created.")
        with open(config_path) as f:
            data = json.load(f)
        self.assertIn("user_calendar_id", data, "Config file missing 'user_calendar_id' key.")
        self.assertIn("clinic_calendar_id", data, "Config file missing 'clinic_calendar_id' key.")

    def test_config_overwrite(self):
        """Test that running configure() again updates the config file with new values."""
        config_path = os.path.expanduser("~/.clinic-config")
        # Write initial config
        initial = {"user_calendar_id": "<EMAIL>", "clinic_calendar_id": "<EMAIL>"}
        with open(config_path, "w") as f:
            json.dump(initial, f)
        # Overwrite config with new values
        new = {"user_calendar_id": "<EMAIL>", "clinic_calendar_id": "<EMAIL>"}
        with open(config_path, "w") as f:
            json.dump(new, f)
        # Read back and check
        with open(config_path) as f:
            data = json.load(f)
        self.assertEqual(data["user_calendar_id"], "<EMAIL>")
        self.assertEqual(data["clinic_calendar_id"], "<EMAIL>")

    def test_config_error_if_token_missing(self):
        """Test that configure() raises an error if token.json is missing."""
        config_path = os.path.expanduser("~/.clinic-config")
        token_path = os.path.expanduser("~/token.json")
        # Remove token.json if it exists (simulate missing token)
        if os.path.exists(token_path):
            os.rename(token_path, token_path + ".bak")
        try:
            from config import configure
            with self.assertRaises(Exception) as context:
                configure()
            self.assertIn("token.json not found", str(context.exception))
        finally:
            # Restore token.json if it was backed up
            if os.path.exists(token_path + ".bak"):
                os.rename(token_path + ".bak", token_path)

    def test_config_error_if_clinic_calendar_missing(self):
        """Test that configure() prompts or handles gracefully if Coding Clinic calendar is not found."""
        # We'll mock get_calendar_service and the calendar list to simulate no Coding Clinic calendar
        import builtins
        from unittest.mock import patch, MagicMock
        config_path = os.path.expanduser("~/.clinic-config")
        # Remove config file if it exists
        if os.path.exists(config_path):
            os.remove(config_path)
        # Mock calendar list to have only a primary calendar, no Coding Clinic
        fake_calendar_list = {'items': [
            {'id': '<EMAIL>', 'primary': True, 'summary': 'My Calendar'}
        ]}
        with patch('config.get_calendar_service') as mock_service:
            mock_service.return_value.calendarList.return_value.list.return_value.execute.return_value = fake_calendar_list
            # Patch input to simulate user entering a clinic calendar ID
            with patch.object(builtins, 'input', return_value='<EMAIL>'):
                from config import configure
                configure()
        # Check that the config file now contains the manually entered clinic calendar ID
        with open(config_path) as f:
            data = json.load(f)
        self.assertEqual(data['clinic_calendar_id'], '<EMAIL>')


