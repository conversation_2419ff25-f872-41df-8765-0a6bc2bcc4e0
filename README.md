# 🧠 Coding Clinic Booking System (Command-Line Tool)

## 🚀 Group Project @ WeThinkCode_

This is a collaborative software project designed to help students easily **book**, **manage**, and **volunteer for Coding Clinic sessions** via a set of **command-line tools** that interface with **Google Calendar**. The goal is to streamline one-on-one mentorship by automating the scheduling and availability process.

> This project runs over **5 weekly iterations**, giving us the opportunity to work in a real team environment and apply our learning.

---

## 📚 Project Overview

**Coding Clinics** at WeThinkCode_ allow students to seek help from experienced peers. However, booking a session manually can be cumbersome. Our CLI tool addresses this by enabling students to:

- 🔍 View available slots
- 📅 Book a 30-minute session
- 🙋 Volunteer to mentor
- ❌ Cancel their bookings
- 🔄 Synchronize with **Google Calendar**

All of this is done from the **Linux terminal** and built with **automation, flexibility, and simplicity** in mind.

---

## 🎯 Core Features

| Feature                  | Description                                                                 |
|--------------------------|-----------------------------------------------------------------------------|
| 🛠️ Configuration         | Connect student & Coding Clinic calendars via Google Calendar API           |
| 📆 View Calendars        | Display upcoming 7-day schedule from both calendars                         |
| 📝 Book a Slot           | Schedule a session with time, date, and help description                    |
| 🤝 Volunteer Availability| Allow students to offer availability to mentor                              |
| ❌ Cancel Booking        | Allow students to cancel their own bookings                                 |
| ❌ Cancel Volunteering   | Allow volunteers to cancel their availability                               |

---

## 💻 How It Works

### 🔧 Configuration
Students must first configure the system to connect to:
- Their personal Google Calendar
- The Coding Clinic shared Google Calendar

what does this part do:


from google_auth_oauthlib.flow import InstalledAppFlow


SCOPES = ['https://www.googleapis.com/auth/calendar']


def login():

    flow = InstalledAppFlow.from_client_secrets_file(

        'client_secret.json', SCOPES)


    Their personal Google Calendar
    The Coding Clinic shared Google Calendar

This configuration is stored in a hidden file in the user’s home directory, e.g., ~/.clinic-config.
🧪 Terminal Input/Output Examples
1️⃣ Configuration

          

$ clinic config --setup
🔗 Connecting to Google Calendar...
✅ Connected to Student Calendar: <EMAIL>
✅ Connected to Coding Clinic Calendar: <EMAIL>
💾 Configuration saved to ~/.clinic-config

      

🚀 User "Login" Concept (Enhanced with User Identification)

Objective: Allow multiple users to log in to their Google accounts, so their credentials are stored securely and separately, mimicking a login experience.
Implementation Overview:

    Separate credentials per user, identified by username or email.
    Credentials stored in ~/.clinic-config-<username>.json.
    Commands:
        clinic login --user <username> — Initiate login/authentication for a specific user.
        clinic verify --user <username> — Verify if user is authenticated.
        clinic logout --user <username> — Remove stored credentials for that user.

🧪 Terminal Input/Output Examples for User Login

          

$ clinic login --user johndoe
🔗 Connecting to Google Calendar for johndoe...
✅ Authentication successful. Credentials stored.

$ clinic verify --user johndoe
johndoe is logged in and authenticated.

$ clinic logout --user johndoe
Credentials for johndoe have been removed.

      

Key Points:

    When a user logs in, the system prompts for their Google account authentication.
    Credentials are stored securely in a per-user JSON file.
    During operations (view, book, cancel), the system uses the logged-in user's credentials.
    Multiple users can be managed on the same system, each with their own credentials.

---

### 🧪 Terminal Input/Output Examples

#### 1️⃣ Configuration

```bash
$ clinic config --setup
🔗 Connecting to Google Calendar...
✅ Connected to Student Calendar: <EMAIL>
✅ Connected to Coding Clinic Calendar: <EMAIL>
💾 Configuration saved to ~/.clinic-config


$ clinic view --days 7
📅 Coding Clinic Availability: 26 May – 01 June 2025

+------------+--------+------------------+----------------------------+
|   Date     | Time   | Volunteer        | Booked By / Description    |
+------------+--------+------------------+----------------------------+
| 2025-05-27 | 10:00  | Alice (JHB)      | Available                  |
| 2025-05-27 | 11:00  | Alice (JHB)      | Booked by: Bob             |
|            |        |                  | "Need help with recursion" |
| 2025-05-28 | 14:00  | John (CPT)       | Available                  |
+------------+--------+------------------+----------------------------+

$ clinic book --date 2025-05-27 --time 10:00 --desc "Struggling with linked lists"
🔍 Checking slot availability for 2025-05-27 at 10:00...
✅ Slot is available and has a volunteer assigned.
📅 Booking confirmed.
📤 Synced to your calendar.
💾 Local data updated.


$ clinic volunteer --date 2025-05-29 --time 15:00
🔍 Checking if slot is free...
✅ Slot is available.
🤝 You’ve been added as a volunteer for 2025-05-29 at 15:00.
📤 Synced to your calendar.
💾 Local data updated.


$ clinic cancel-booking --date 2025-05-27 --time 11:00
🔍 Locating your booking...
✅ Booking found: Bob | "Need help with recursion"
❌ Booking canceled.
📤 Calendar updated.
💾 Local data updated.

$ clinic cancel-volunteer --date 2025-05-29 --time 15:00
🔍 Verifying no student has booked this slot...
✅ Slot is free from student bookings.
🤷 You are no longer marked as a volunteer for this slot.
📤 Calendar updated.
💾 Local data updated.


$ clinic --help
🧠 Coding Clinic Booking CLI

Usage:
  clinic [command] [options]

Commands:
  config                Set up Google Calendar connection
  view                  View available slots (default: next 7 days)
  book                  Book a session
  volunteer             Sign up to volunteer
  cancel-booking        Cancel your own booking
  cancel-volunteer      Cancel your volunteer slot
  help                  Show this help message

Options:
  --date YYYY-MM-DD     Specify date
  --time HH:MM          Specify time
  --desc "text"         Add a description
  --days N              Number of days to view


Data Storage:

    Stored locally in .json

    Cached data files are updated every run if necessary

    Hidden configuration and data files keep project clean

🧠 What You'll Learn

    👨‍💻 Collaborative coding with Git & GitHub

    📌 Task management using Miro boards

    📣 Communication with teammates & mentors

    📦 Delivering incremental, functional software

    📽️ Iteration reviews and demonstrations


👨‍👩‍👧 Team Members

    Israel Mbuyu

    Olebogeng Kutlwano 

    Botlhale Selome

    Waborena Madisha
    
 🛠️ Tech Stack:

    Bash / Python / Node.js (based on team decision)

    Google Calendar API

    JSON

    Linux CLI tools

📄 License:

This project is developed as part of a WeThinkCode_ educational assignment.
