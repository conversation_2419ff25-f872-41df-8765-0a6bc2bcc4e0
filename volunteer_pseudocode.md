# Volunteer System Pseudo Code

## Core Data Structures

```
volunteer_data = {
    "volunteers": [
        {
            "email": "<EMAIL>",
            "slots": [
                {
                    "date": "2025-01-20",
                    "time": "10:00",
                    "calendar_event_id": "google_event_id_123",
                    "status": "active"
                }
            ]
        }
    ],
    "slot_assignments": {
        "2025-01-20_10:00": {
            "volunteer_email": "<EMAIL>",
            "event_id": "google_event_id_123",
            "created_at": "2025-01-15T09:00:00Z"
        }
    }
}
```

## Function Pseudo Code

### 1. check_slot_availability(service, calendar_id, date, time)
```
FUNCTION check_slot_availability(service, calendar_id, date, time):
    // Convert date/time to datetime objects
    start_datetime = combine(date, time)
    end_datetime = start_datetime + 1_hour
    
    // Query Google Calendar for existing events
    events = service.events().list(
        calendarId=calendar_id,
        timeMin=start_datetime.isoformat(),
        timeMax=end_datetime.isoformat()
    )
    
    // Check local volunteer data for conflicts
    slot_key = f"{date}_{time}"
    local_assignment = load_volunteer_data().slot_assignments.get(slot_key)
    
    IF events.items OR local_assignment:
        RETURN False, "Slot already taken"
    ELSE:
        RETURN True, "Slot available"
```

### 2. check_volunteer_conflicts(volunteer_email, date, time)
```
FUNCTION check_volunteer_conflicts(volunteer_email, date, time):
    volunteer_data = load_volunteer_data()
    
    FOR volunteer IN volunteer_data.volunteers:
        IF volunteer.email == volunteer_email:
            FOR slot IN volunteer.slots:
                IF slot.date == date AND slot.time == time:
                    RETURN True, "You already volunteered for this slot"
                
                // Check for time conflicts (overlapping hours)
                IF slot.date == date AND time_overlap(slot.time, time):
                    RETURN True, "Conflicts with existing volunteer slot"
    
    RETURN False, "No conflicts found"
```

### 3. volunteer_for_slot(service, calendar_id, date, time, volunteer_email)
```
FUNCTION volunteer_for_slot(service, calendar_id, date, time, volunteer_email):
    // Step 1: Validate inputs
    IF NOT valid_date(date) OR NOT valid_time(time):
        RETURN False, "Invalid date or time format"
    
    // Step 2: Check slot availability
    available, message = check_slot_availability(service, calendar_id, date, time)
    IF NOT available:
        RETURN False, message
    
    // Step 3: Check volunteer conflicts
    conflict, conflict_msg = check_volunteer_conflicts(volunteer_email, date, time)
    IF conflict:
        RETURN False, conflict_msg
    
    // Step 4: Create calendar event
    event_details = {
        "summary": f"Code Clinic - Volunteer ({volunteer_email})",
        "start": {"dateTime": f"{date}T{time}:00"},
        "end": {"dateTime": f"{date}T{add_hour(time)}:00"},
        "description": f"Volunteering for Code Clinic session"
    }
    
    calendar_event = add_volunteer_to_calendar(service, calendar_id, event_details)
    
    // Step 5: Save to local data
    volunteer_info = {
        "email": volunteer_email,
        "date": date,
        "time": time,
        "event_id": calendar_event.id,
        "status": "active"
    }
    
    save_volunteer_data(volunteer_info)
    
    RETURN True, "Successfully volunteered for slot"
```

### 4. add_volunteer_to_calendar(service, calendar_id, event_details)
```
FUNCTION add_volunteer_to_calendar(service, calendar_id, event_details):
    TRY:
        event = service.events().insert(
            calendarId=calendar_id,
            body=event_details
        ).execute()
        
        RETURN event
    
    CATCH GoogleAPIError as e:
        RAISE VolunteerBookingError(f"Failed to add to calendar: {e}")
```

### 5. save_volunteer_data(volunteer_info)
```
FUNCTION save_volunteer_data(volunteer_info):
    data = load_volunteer_data()
    
    // Add to volunteers list
    volunteer_found = False
    FOR volunteer IN data.volunteers:
        IF volunteer.email == volunteer_info.email:
            volunteer.slots.append({
                "date": volunteer_info.date,
                "time": volunteer_info.time,
                "calendar_event_id": volunteer_info.event_id,
                "status": volunteer_info.status
            })
            volunteer_found = True
            BREAK
    
    IF NOT volunteer_found:
        data.volunteers.append({
            "email": volunteer_info.email,
            "slots": [{
                "date": volunteer_info.date,
                "time": volunteer_info.time,
                "calendar_event_id": volunteer_info.event_id,
                "status": volunteer_info.status
            }]
        })
    
    // Add to slot assignments
    slot_key = f"{volunteer_info.date}_{volunteer_info.time}"
    data.slot_assignments[slot_key] = {
        "volunteer_email": volunteer_info.email,
        "event_id": volunteer_info.event_id,
        "created_at": current_timestamp()
    }
    
    // Save to file
    write_json_file(VOLUNTEER_DATA_PATH, data)
```

## CLI Integration Pseudo Code

```
// Add to clinic.py main() function
volunteer_parser = subparsers.add_parser("volunteer", help="Volunteer for a time slot")
volunteer_parser.add_argument("--date", required=True, help="Date (YYYY-MM-DD)")
volunteer_parser.add_argument("--time", required=True, help="Time (HH:MM)")

// In main() command handling:
ELIF args.command == "volunteer":
    result, message = volunteer_for_slot(
        service=calendar_service,
        calendar_id=clinic_calendar_id,
        date=args.date,
        time=args.time,
        volunteer_email=user_email
    )
    
    IF result:
        print(f"✅ {message}")
        print("📤 Synced to your calendar.")
        print("💾 Local data updated.")
    ELSE:
        print(f"❌ {message}")
```

## Test Cases to Implement

1. **test_check_slot_availability_free_slot()**
2. **test_check_slot_availability_occupied_slot()**
3. **test_check_volunteer_conflicts_no_conflicts()**
4. **test_check_volunteer_conflicts_same_slot()**
5. **test_check_volunteer_conflicts_overlapping_time()**
6. **test_volunteer_for_slot_success()**
7. **test_volunteer_for_slot_slot_taken()**
8. **test_volunteer_for_slot_volunteer_conflict()**
9. **test_save_volunteer_data_new_volunteer()**
10. **test_save_volunteer_data_existing_volunteer()**
