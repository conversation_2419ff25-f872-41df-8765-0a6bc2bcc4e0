import os
import json
from fetch_display_7days import get_upcoming_events
import datetime

# Local JSON cache path
DATA_FILE_PATH = os.path.expanduser("~/.clinicbooker/calendar_data.json")


def save_events(events):
    os.makedirs(os.path.dirname(DATA_FILE_PATH), exist_ok=True)
    with open(DATA_FILE_PATH, "w") as f:
        json.dump(events, f, indent=2)

def load_events():
    if not os.path.exists(DATA_FILE_PATH):
        return []
    try:
        with open(DATA_FILE_PATH, "r") as f:
            return json.load(f)
    except json.JSONDecodeError:
        return []

def events_are_different(new_events):
    old_events = load_events()
    return new_events != old_events

# local_cache.py (or wherever display_events is)

def display_events(events):
    """Display a list of events sorted by start time."""  # UPDATED docstring

    if not events:
        print("No events to display.")
        return

    # Sort events by start time
    def event_start(event):
        start_raw = event.get("start", {}).get("dateTime") or event.get("start", {}).get("date")
        try:
            return datetime.datetime.fromisoformat(start_raw)
        except Exception:
            return datetime.datetime.min

    events.sort(key=event_start)

    for event in events:
        start = event.get("start", {}).get("dateTime") or event.get("start", {}).get("date") or "Unknown"
        summary = event.get("summary", "No summary")
        print(f"{start}: {summary}")


def update_or_load_events(fetch_func):
    new_events = fetch_func()
    if events_are_different(new_events):
        print("New calendar data found, updating local cache.")
        save_events(new_events)
    else:
        print("No new updates found. Using cached calendar data.")
        new_events = load_events()  # Reuse cached data
    display_events(new_events)


# local_cache.py (or similar module)

def fetch_and_cache_events(service, calendar_id, days=7):
    """Fetch from Google, update cache if new, else load cache."""  # ADDED

    print("Fetching events from Google Calendar...")
    new_events = get_upcoming_events(service, calendar_id, days=days)

    if events_are_different(new_events):  # uses your existing function
        print("New calendar data found, updating local cache.")
        save_events(new_events)  # uses your existing save_events()
        return new_events
    else:
        print("No new updates found. Using cached calendar data.")
        return load_events()  # uses your existing load_events()
    

sample_events = [
    {
        "start": {"dateTime": "2025-05-29T13:00:00"},
        "summary": "Test Event 1"
    },
    {
        "start": {"date": "2025-05-30"},
        "summary": "All-Day Event"
    }
]
# Step 1: Save sample
save_events(sample_events)

# Step 2: Load and display
loaded = load_events()
display_events(loaded)

# Step 3: Test difference detection
print(events_are_different(sample_events))  # Expect False

# Step 4: Change sample and test difference
different_events = [
    {
        "start": {"dateTime": "2025-05-29T14:00:00"},
        "summary": "Another Event"
    }
]
print(events_are_different(different_events))  # Expect True
