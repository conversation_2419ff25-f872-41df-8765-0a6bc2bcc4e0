import os
import json
from google.oauth2.credentials import Credentials
from googleapiclient.discovery import build

CONFIG_PATH = os.path.expanduser("~/.clinic-config")
DEFAULT_CONFIG_PATH = os.path.join(os.path.dirname(__file__), ".clinicbooker/config.json")
TOKEN_PATH = os.path.expanduser("~/token.json")
SCOPES = ['https://www.googleapis.com/auth/calendar']

def get_calendar_service():
    """Authenticate and return a Google Calendar API service object."""
    if not os.path.exists(TOKEN_PATH):
        raise Exception("Google API token.json not found. Please login first.")
    creds = Credentials.from_authorized_user_file(TOKEN_PATH, SCOPES)
    service = build('calendar', 'v3', credentials=creds)
    return service

def configure():
    """
    Create ~/.clinic-config with user and clinic calendar IDs.
    If .clinicbooker/config.json exists, use its values.
    Otherwise, fetch the user's primary calendar and try to find the Coding Clinic calendar.
    """
    config = {}
    # Try to load from .clinicbooker/config.json if it exists
    if os.path.exists(DEFAULT_CONFIG_PATH):
        with open(DEFAULT_CONFIG_PATH) as f:
            config = json.load(f)
    else:
        service = get_calendar_service()
        calendar_list = service.calendarList().list().execute()
        user_calendar_id = None
        clinic_calendar_id = None
        # Find primary calendar (user's main calendar)
        for calendar in calendar_list['items']:
            if calendar.get('primary'):
                user_calendar_id = calendar['id']
            # Try to find Coding Clinic calendar by name
            if 'coding' in calendar['summary'].lower() and 'clinic' in calendar['summary'].lower():
                clinic_calendar_id = calendar['id']
        if not user_calendar_id:
            user_calendar_id = input("Could not auto-detect your primary calendar. Enter your Google Calendar ID (student email): ").strip()
        if not clinic_calendar_id:
            clinic_calendar_id = input("Could not auto-detect Coding Clinic calendar. Enter the Coding Clinic Calendar ID: ").strip()
        config = {
            "user_calendar_id": user_calendar_id,
            "clinic_calendar_id": clinic_calendar_id
        }
    # Save to ~/.clinic-config
    with open(CONFIG_PATH, "w") as f:
        json.dump(config, f, indent=2)



