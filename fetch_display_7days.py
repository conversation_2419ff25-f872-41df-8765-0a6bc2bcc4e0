import datetime
from prettytable import PrettyTable
from googleapiclient.errors import HttpError


def get_upcoming_events(service, calendar_id, days=7):
    """function that fetches 7 days worth of information from both the user and the clinic's calendar"""

    daily_events = {}

    try:
        now = datetime.datetime.now(datetime.timezone.utc)

        print(f"\nGetting the next 07 days from calendar: {calendar_id}")

        for i in range(7):
            day = now + datetime.timedelta(days = i)
            start_day = day.replace(hour=0, minute=0, second=0, microsecond=0)
            end_day = start_day + datetime.timedelta(days = 1)

            time_min = start_day.isoformat()
            time_max = end_day.isoformat()

            events_result = (
                service.events()
                .list(
                    calendarId=calendar_id,
                    timeMin=time_min,
                    timeMax=time_max,
                    singleEvents=True,
                    orderBy="startTime",
                )
                .execute()
            )
            events = events_result.get("items", [])
            daily_events[day.date().isoformat()] = events


        display_events(daily_events)

        return daily_events

    except HttpError as error:
        print(f"An error occurred: {error}")


def display_events(daily_events):
    """Function displays the information from the Google calendar in prettytable format"""
    
    if not daily_events:
        print("No upcoming events found.")
        return 
 
    
    for day, events in daily_events.items():
        # Convert day string to date object for formatting
        try:
            day_obj = datetime.datetime.fromisoformat(day).date()
            day_str = day_obj.strftime('%A, %d/%m/%Y')
        except Exception:
            day_str = str(day)
        print(f"\n📅 {day_str}:")

        if not events:
            print("No upcoming events found.")
            continue 

        display_format = PrettyTable(["Time", "Scheduled"])

    # Prints the start and name of the next 7 events
        for event in events:
            start_raw = event["start"].get("dateTime", event["start"].get("date"))

            try:
                dt = datetime.datetime.fromisoformat(start_raw)
                if "T" in start_raw:
                    start = dt.strftime("%I:%M %p")
                else:
                    start = dt.strftime("%A (All Day)")
            
            except ValueError:
                start = start_raw

            summary = event.get("summary", "No Title")
            display_format.add_row([start, summary])
            
        print(display_format)



if __name__ == "__main__":
    from calendar_sync import main
    run = main()


