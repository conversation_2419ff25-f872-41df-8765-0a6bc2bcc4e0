import subprocess
import os
import unittest

class TestCLI(unittest.TestCase):
    def setUp(self):
        self.config_path = os.path.expanduser("~/.clinic-config")
        # Remove config file if it exists
        if os.path.exists(self.config_path):
            os.remove(self.config_path)

    def test_cli_config_command(self):
        """Test that running 'python clinic.py config --setup' creates the config file."""
        result = subprocess.run([
            "python3", "clinic.py", "config", "--setup"
        ], capture_output=True, text=True)
        self.assertTrue(os.path.exists(self.config_path), "Config file was not created by CLI config command.")
        self.assertIn("Configuration complete", result.stdout)

    def test_cli_view_command(self):
        """Test that running 'python clinic.py view --days 7' prints calendar output."""
        # Create a dummy config file so view doesn't fail
        with open(self.config_path, "w") as f:
            f.write('{"user_calendar_id": "dummy", "clinic_calendar_id": "dummy"}')
        result = subprocess.run([
            "python3", "clinic.py", "view", "--days", "7"
        ], capture_output=True, text=True)
        self.assertIn("📅", result.stdout)

if __name__ == "__main__":
    unittest.main()
