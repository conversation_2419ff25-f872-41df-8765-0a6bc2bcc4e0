from google_auth_oauthlib.flow import InstalledAppFlow
import os
import json

SCOPES = ['https://www.googleapis.com/auth/calendar']

def login():
    flow = InstalledAppFlow.from_client_secrets_file(
        'secrets/client_credentials.json', SCOPES)
    
    creds = flow.run_local_server(port=0)  # Opens browser for login

    # Optional: Save token to a file so you don't have to login every time
    with open("token.json", "w") as token:
        token.write(creds.to_json())


    return 'Login successful'


