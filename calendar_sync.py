import datetime
import os.path
import json

from fetch_display_7days import *
from prettytable import PrettyTable
from google.auth.transport.requests import Request
from google.oauth2.credentials import Credentials
from google_auth_oauthlib.flow import InstalledAppFlow
from googleapiclient.discovery import build

# Google Calendar scope for read access
SCOPES = ["https://www.googleapis.com/auth/calendar.readonly"]



def get_credentials():
    creds = None
    token_path = os.path.expanduser("~/.secrets/token.json")
    client_secret_path = os.path.expanduser("~/.secrets/client_credentials.json")
    if os.path.exists(token_path):
        creds = Credentials.from_authorized_user_file(token_path, SCOPES)
    if not creds or not creds.valid:
        if creds and creds.expired and creds.refresh_token:
            creds.refresh(Request())
        else:
            flow = InstalledAppFlow.from_client_secrets_file(client_secret_path, SCOPES)
            creds = flow.run_local_server(port=0)
        os.makedirs(os.path.dirname(token_path), exist_ok=True)
        with open(token_path, "w") as token:
            token.write(creds.to_json())
    return creds


def load_calendar_config():
    config_path = os.path.expanduser("~/.clinicbooker/config.json")
    print("Looking for config file at:", config_path)
    with open(config_path, "r") as f:
        config = json.load(f)
    return config["user_calendar"], config["clinic_calendar"]

def convert_keys_to_str(obj):
    if isinstance(obj, dict):
        return {str(k): convert_keys_to_str(v) for k, v in obj.items()}
    elif isinstance(obj, list):
        return [convert_keys_to_str(i) for i in obj]
    else:
        return obj

def main():
    creds = get_credentials()
    service = build("calendar", "v3", credentials=creds)

    user_calendar_id, clinic_calendar_id = load_calendar_config()

    print("Which calendar would you like to view?")
    print("1 - My personal calendar")
    print("2 - Code Clinic calendar")
    choice = input("Enter 1 or 2: ")

    if choice == "1":
        calendar_id = user_calendar_id
    elif choice == "2":
        calendar_id = clinic_calendar_id
    else:
        print("Invalid choice. Exiting.")
        return

    events = get_upcoming_events(service, calendar_id)
    
    # Before saving:
    events = convert_keys_to_str(events)
    with open('events.json', 'w') as f:
        json.dump(events, f, indent=2)


if __name__ == "__main__":
    main()
